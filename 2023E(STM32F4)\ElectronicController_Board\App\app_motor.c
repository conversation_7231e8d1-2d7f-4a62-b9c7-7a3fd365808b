#include "mydefine.h"

/**
 * @brief �����ʼ������
 */
void Motor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    Motor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void Motor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}
