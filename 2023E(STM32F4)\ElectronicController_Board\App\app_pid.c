// Copyright (c) 2024 �״׵��ӹ�����

#include "app_pid.h"
#include "pid.h"

// PID������ʵ��
PID_T pid_x; // X��PID������
PID_T pid_y; // Y��PID������

PidParams_t pid_params_x = {
    .kp = 2.0f,   // ����Kp�����Ӧ�ٶ�
    .ki = 0.008f, // ���ӻ������С��̬���
    .kd = 0.015f, // ����΢��������ȶ���
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 1 // ��С������С����߾���
};

PidParams_t pid_params_y = {
    .kp = 2.0f,   // ����Kp�����Ӧ�ٶ�
    .ki = 0.008f, // ���ӻ������С��̬���
    .kd = 0.008f, // ����΢��������ȶ���
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 1 // ��С������С����߾���
};

// PIDĿ������
int target_x = 320; // Ĭ����Ļ����
int target_y = 240; // Ĭ����Ļ����

// ��ǰʵ������
int current_x = 320;
int current_y = 240;

// PID����״̬��־
static bool pid_running = false;

// PID��ʱ��
static MultiTimer mt_pid;

// PID��Ϣ�ϱ���ʱ��
static MultiTimer mt_pid_report;

// ������ֵ
int8_t motor_x, motor_y;

// ���Ա�־����ʾ������Ϣ
#define DEBUG_PID 0

// ���ٱ��ʹ�����
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

/**
 * @brief �����޷�����
 * @param pid PID������
 * @param min ��Сֵ
 * @param max ���ֵ
 */
static void app_pid_limit_integral(PID_T *pid, float min, float max)
{
    if (pid->integral > max)
    {
        pid->integral = max;
    }
    else if (pid->integral < min)
    {
        pid->integral = min;
    }
}

/**
 * @brief ����PID��������ʼ��
 */
void app_pid_init(void)
{
    // ��ʼ��X��PID������
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);

    // ��ʼ��Y��PID������
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);

    // �Զ�����PID����
#if DEBUG_PID
    app_pid_start();
#endif

    // �������PID��Ϣ�ϱ��������ϱ���ʱ��
#if PID_REPORT_ENABLE
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief ����PID������Ŀ������
 * @param x X��Ŀ������
 * @param y Y��Ŀ������
 */
void app_pid_set_target(int x, int y)
{
#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "����Ŀ��λ��: X=%d, Y=%d\n", x, y);
#endif
    target_x = x;
    target_y = y;

    // ʹ�����ǵ�PID�м������Ŀ��ֵ
    pid_set_target(&pid_x, (float)target_x);
    pid_set_target(&pid_y, (float)target_y);
}

/**
 * @brief ���µ�ǰʵ������
 * @param x X�ᵱǰ����
 * @param y Y�ᵱǰ����
 */
void app_pid_update_position(int x, int y)
{
    // ���������Ч��
    if (x < 0 || x > 1920 || y < 0 || y > 1080)
    {
        return; // ��Ч����ֱ�Ӷ���
    }

    current_x = x;
    current_y = y;
}

/**
 * @brief ����PID�������
 * ͨ��PID�㷨�������ٶȿ���ֵ
 */
void app_pid_calc(void)
{
    int error_x, error_y;
    float output_x, output_y;

    // ����X�����
    error_x = target_x - current_x;
    error_y = target_y - current_y;

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "���X=%d Y=%d\n", error_x, error_y);
#endif

    // ֻ��ͬʱ�������ڲ�ֹͣ���
    if (abs(error_x) <= pid_params_x.deadzone && abs(error_y) <= pid_params_y.deadzone)
    {
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "�������ڣ�ֹͣ���\n");
#endif
        Motor_Stop();
        return;
    }

    // ʹ��λ��ʽPID����X�����
    output_x = pid_calculate_positional(&pid_x, (float)current_x);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);

    // ʹ��λ��ʽPID����Y�����
    output_y = pid_calculate_positional(&pid_y, (float)current_y);

    // ִ�л����޷�
    app_pid_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);

    // �޷�����
    output_x = CONSTRAIN(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y = CONSTRAIN(output_y, pid_params_y.out_min, pid_params_y.out_max);

    // ��ȡ���յ������ֵ
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "PID���: X=%d, Y=%d\n", motor_x, motor_y);
#endif

    // ���Ƶ��
    Motor_Set_Speed(-motor_x, -motor_y);
}

/**
 * @brief ��������ص�����
 * ������ͷ��⵽��������ʱ����
 * @param coord ��������ṹ��
 */
void pid_laser_coord_callback(LaserCoord_t coord)
{
    // ֻ������ɫ�������꣨�ɸ�����Ҫ�޸�Ϊ������ɫ���⣩
    if (coord.type == RED_LASER_ID)
    {
        // ���µ�ǰλ��
        app_pid_update_position(coord.x, coord.y);
    }
		else if(coord.type == GREEN_LASER_ID)
		{
			app_pid_set_target(coord.x, coord.y);
		}
}

/**
 * @brief PID����������
 * �ɶ�ʱ���ص���������ִ��PID����
 */
void app_pid_task(MultiTimer *timer, void *userData)
{
    if (pid_running)
    {
        // ִ��PID����
        app_pid_calc();

        // ����������ʱ����ʵ�����ڵ���
        multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);
    }
}

/**
 * @brief ����PID����
 */
void app_pid_start(void)
{
    // ���PID�����Ѿ������У�ֱ�ӷ���
    if (pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "����PID����\n");
#endif

    // ���ü�������ص�����
    maixcam_set_callback(pid_laser_coord_callback);

    // ����PID������
    pid_reset(&pid_x);
    pid_reset(&pid_y);

    // ����PID���б�־
    pid_running = true;

    // ����PID����ʱ��
    multiTimerStart(&mt_pid, (uint32_t)(pid_params_x.sample_time * 1000), app_pid_task, NULL);

    // �������PID��Ϣ�ϱ��������ϱ���ʱ��
#if PID_REPORT_ENABLE
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief ֹͣPID����
 */
void app_pid_stop(void)
{
    // ���PID������ֹͣ��ֱ�ӷ���
    if (!pid_running)
    {
        return;
    }

#if DEBUG_PID
    my_printf(&PID_REPORT_UART, "ֹͣPID����\n");
#endif

    // ֹͣ���
    Motor_Stop();

    // ֹͣPID����ʱ��
    multiTimerStop(&mt_pid);

    // �������PID��Ϣ�ϱ���ֹͣ�ϱ���ʱ��
#if PID_REPORT_ENABLE
    multiTimerStop(&mt_pid_report);
#endif

    // ���PID���б�־
    pid_running = false;

    // �ָ�Ĭ�ϻص�����
    maixcam_set_callback(NULL);
}

/**
 * @brief ����X��PID����
 * @param params �µ�PID�����ṹ��
 */
void app_pid_set_x_params(PidParams_t params)
{
    // ���²����ṹ��
    pid_params_x = params;

    // ����PID����������
    pid_set_params(&pid_x, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_x, params.out_max);
}

/**
 * @brief ����Y��PID����
 * @param params �µ�PID�����ṹ��
 */
void app_pid_set_y_params(PidParams_t params)
{
    // ���²����ṹ��
    pid_params_y = params;

    // ����PID����������
    pid_set_params(&pid_y, params.kp, params.ki, params.kd);
    pid_set_limit(&pid_y, params.out_max);
}

/**
 * @brief ������λ��Ŀ��ֵ
 * @param scale_value �Ŵ���ֵ
 * @return ʵ��ֵ
 */
int app_pid_parse_target(int scale_value)
{
    // ������λ�����ݸ�ʽ��ת��Ϊʵ������ֵ
    return scale_value / PID_REPORT_SCALE;
}

/**
 * @brief PID��Ϣ�ϱ�����
 * ����ָ����ʽ���PID��������Ŀ��ֵ��ʵ��ֵ
 */
void app_pid_report(void)
{
    // ��PIDĿ��ֵ��ʵ��ֵ�Ŵ�ת��Ϊ����
    int scaled_target_x = (int)(target_x * PID_REPORT_SCALE);
    int scaled_current_x = (int)(current_x * PID_REPORT_SCALE);
    int scaled_target_y = (int)(target_y * PID_REPORT_SCALE);
    int scaled_current_y = (int)(current_y * PID_REPORT_SCALE);

    // ����{name}a,b\n��ʽ���PID��Ϣ
    my_printf(&PID_REPORT_UART, "{X}%d,%d\n", scaled_target_x, scaled_current_x);
    my_printf(&PID_REPORT_UART, "{Y}%d,%d\n", scaled_target_y, scaled_current_y);
    my_printf(&PID_REPORT_UART, "{V}%d,%d\n", motor_x, motor_y);

    // ������������Ϣ
#if PID_DEBUG_ENABLE
    my_printf(&PID_REPORT_UART, "{PX}%.2f,%.2f,%.2f\n", pid_x.p_out, pid_x.i_out, pid_x.d_out);
    my_printf(&PID_REPORT_UART, "{PY}%.2f,%.2f,%.2f\n", pid_y.p_out, pid_y.i_out, pid_y.d_out);
#endif
}

/**
 * @brief PID��Ϣ�ϱ�����
 * �ɶ�ʱ���ص���������ִ��PID��Ϣ�ϱ�
 */
void app_pid_report_task(MultiTimer *timer, void *userData)
{
#if PID_REPORT_ENABLE
    // ִ��PID��Ϣ�ϱ�
    app_pid_report();

    // ����������ʱ����ʵ�����ڵ���
    multiTimerStart(&mt_pid_report, PID_REPORT_PERIOD, app_pid_report_task, NULL);
#endif
}

/**
 * @brief ������λ��ָ��
 * @param cmd ��λ��ָ���ַ���
 */
void app_pid_parse_cmd(char *cmd)
{
    int val1, val2, val3;

    // ������λ�����õ�Ŀ��λ��
    if (sscanf(cmd, "T:%d,%d", &val1, &val2) == 2)
    {
        target_x = app_pid_parse_target(val1);
        target_y = app_pid_parse_target(val2);

        // ����PIDĿ��ֵ
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);

#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "�յ�Ŀ��λ������: X=%d, Y=%d\n", target_x, target_y);
#endif
    }
    // ����X��PID�������� - PX:kp,ki,kd
    else if (sscanf(cmd, "PX:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_x = pid_params_x;
        new_params_x.kp = val1 / 100.0f;
        new_params_x.ki = val2 / 100.0f;
        new_params_x.kd = val3 / 100.0f;
        app_pid_set_x_params(new_params_x);
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "����X��PID����: kp=%.2f, ki=%.2f, kd=%.2f\n",
                  new_params_x.kp, new_params_x.ki, new_params_x.kd);
#endif
    }
    // ����Y��PID�������� - PY:kp,ki,kd
    else if (sscanf(cmd, "PY:%d,%d,%d", &val1, &val2, &val3) == 3)
    {
        PidParams_t new_params_y = pid_params_y;
        new_params_y.kp = val1 / 100.0f;
        new_params_y.ki = val2 / 100.0f;
        new_params_y.kd = val3 / 100.0f;
        app_pid_set_y_params(new_params_y);
#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "����Y��PID����: kp=%.2f, ki=%.2f, kd=%.2f\n",
                  new_params_y.kp, new_params_y.ki, new_params_y.kd);
#endif
    }
    // ��������/ֹͣ����
    else if (strncmp(cmd, "START", 5) == 0)
    {
        app_pid_start();
    }
    else if (strncmp(cmd, "STOP", 4) == 0)
    {
        app_pid_stop();
    }
    // ֱ������λ������ R: X=110, Y=102
    else if (sscanf(cmd, "R: X=%d, Y=%d", &val1, &val2) == 2)
    {
        target_x = val1;
        target_y = val2;

        // ����PIDĿ��ֵ
        pid_set_target(&pid_x, (float)target_x);
        pid_set_target(&pid_y, (float)target_y);

#if DEBUG_PID
        my_printf(&PID_REPORT_UART, "ֱ������Ŀ��λ��: X=%d, Y=%d\n", target_x, target_y);
        // ��һ�ζ�ȡ������ֵ
        app_pid_init();
        // ����Ĭ��Ŀ��λ��
        app_pid_set_target(target_x, target_y);
#endif
        // ���PIDδ�������Զ�����
        if (!pid_running)
        {
            app_pid_start();
        }
    }
}
